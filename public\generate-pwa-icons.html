<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PWA Icon Generator - DERS</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #F00F00, #000000);
            color: white;
            min-height: 100vh;
        }
        .container {
            background: rgba(255,255,255,0.1);
            padding: 30px;
            border-radius: 15px;
            backdrop-filter: blur(10px);
        }
        h1 {
            color: #FFC20F;
            text-align: center;
            margin-bottom: 30px;
        }
        .icon-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-top: 30px;
        }
        .icon-item {
            background: rgba(255,255,255,0.1);
            padding: 20px;
            border-radius: 10px;
            text-align: center;
        }
        canvas {
            border: 2px solid #FFC20F;
            border-radius: 10px;
            margin: 10px 0;
            background: white;
        }
        button {
            background: #FFC20F;
            color: #000;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            font-weight: bold;
            margin: 5px;
        }
        button:hover {
            background: #E6B00E;
        }
        .instructions {
            background: rgba(255,255,255,0.1);
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
        }
        .logo-preview {
            text-align: center;
            margin: 20px 0;
        }
        #sourceImage {
            max-width: 200px;
            border: 2px solid #FFC20F;
            border-radius: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>DERS PWA Icon Generator</h1>
        
        <div class="instructions">
            <h3>Instructions:</h3>
            <ol>
                <li>The system logo will be automatically loaded and converted to PWA icons</li>
                <li>Click "Generate All Icons" to create all required sizes</li>
                <li>Right-click each generated icon and "Save image as..." to download</li>
                <li>Save the icons to: <code>public/assets/system_img/pwa-icons/</code></li>
                <li>Use the exact filenames shown (e.g., icon-72x72.png, icon-96x96.png, etc.)</li>
            </ol>
        </div>

        <div class="logo-preview">
            <h3>Source Logo:</h3>
            <img id="sourceImage" src="assets/system_img/system-logo.png" alt="DERS Logo" crossorigin="anonymous">
        </div>

        <div style="text-align: center; margin: 20px 0;">
            <button onclick="generateAllIcons()">Generate All Icons</button>
            <button onclick="downloadAllIcons()">Download All Icons</button>
        </div>

        <div class="icon-grid" id="iconGrid">
            <!-- Icons will be generated here -->
        </div>
    </div>

    <script>
        const iconSizes = [72, 96, 128, 144, 152, 192, 384, 512];
        let generatedIcons = {};

        function generateAllIcons() {
            const sourceImg = document.getElementById('sourceImage');
            const iconGrid = document.getElementById('iconGrid');
            iconGrid.innerHTML = '';

            iconSizes.forEach(size => {
                const iconItem = document.createElement('div');
                iconItem.className = 'icon-item';
                
                const canvas = document.createElement('canvas');
                canvas.width = size;
                canvas.height = size;
                canvas.id = `icon-${size}`;
                
                const ctx = canvas.getContext('2d');
                
                // Create a white background
                ctx.fillStyle = '#FFFFFF';
                ctx.fillRect(0, 0, size, size);
                
                // Calculate padding (10% of size)
                const padding = size * 0.1;
                const logoSize = size - (padding * 2);
                
                // Draw the logo centered with padding
                ctx.drawImage(sourceImg, padding, padding, logoSize, logoSize);
                
                // Store the canvas data
                generatedIcons[size] = canvas.toDataURL('image/png');
                
                const title = document.createElement('h4');
                title.textContent = `${size}x${size}px`;
                title.style.color = '#FFC20F';
                
                const downloadBtn = document.createElement('button');
                downloadBtn.textContent = 'Download';
                downloadBtn.onclick = () => downloadIcon(size);
                
                iconItem.appendChild(title);
                iconItem.appendChild(canvas);
                iconItem.appendChild(downloadBtn);
                iconGrid.appendChild(iconItem);
            });
        }

        function downloadIcon(size) {
            const link = document.createElement('a');
            link.download = `icon-${size}x${size}.png`;
            link.href = generatedIcons[size];
            link.click();
        }

        function downloadAllIcons() {
            iconSizes.forEach(size => {
                if (generatedIcons[size]) {
                    setTimeout(() => downloadIcon(size), size); // Stagger downloads
                }
            });
        }

        // Auto-generate icons when image loads
        document.getElementById('sourceImage').onload = function() {
            setTimeout(generateAllIcons, 500);
        };

        // If image is already loaded
        if (document.getElementById('sourceImage').complete) {
            setTimeout(generateAllIcons, 500);
        }
    </script>
</body>
</html>
