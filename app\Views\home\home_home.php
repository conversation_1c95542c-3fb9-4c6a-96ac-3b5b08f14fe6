<?= $this->extend('templates/home_template') ?>

<?= $this->section('css') ?>
<style>
    /* Page-specific styles for home page */
    .job-card {
      position: relative;
      overflow: hidden;
    }

    .job-card::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      width: 4px;
      height: 100%;
      background: linear-gradient(to bottom, var(--yellow), var(--yellow-dark));
      border-radius: 4px 0 0 4px;
    }

    .stat-card {
      background-color: white;
      padding: 2rem;
      border-radius: 12px;
      box-shadow: 0 10px 25px -3px rgba(0, 0, 0, 0.08);
      transform: translateY(0);
      transition: all 0.3s ease;
      border: none;
    }

    .stat-card:hover {
      transform: translateY(-0.35rem);
      box-shadow: 0 15px 30px -5px rgba(0, 0, 0, 0.1);
    }

    /* Toggle view styles */
    .view-toggle {
      background: white;
      border-radius: 8px;
      padding: 4px;
      box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    }

    .view-toggle .btn {
      border: none;
      padding: 8px 16px;
      border-radius: 4px;
      transition: all 0.3s ease;
    }

    .view-toggle .btn.active {
      background: var(--red);
      color: white;
    }

    .view-toggle .btn:not(.active) {
      background: transparent;
      color: var(--red);
    }

    .view-toggle .btn:not(.active):hover {
      background: rgba(220, 53, 69, 0.1);
    }

    /* Table view styles */
    .table-view {
      display: none;
    }

    .table-view.active {
      display: block;
    }

    .grid-view.active {
      display: block;
    }

    .grid-view:not(.active) {
      display: none;
    }

    .positions-table {
      background: white;
      border-radius: 12px;
      overflow: hidden;
      box-shadow: 0 10px 25px -3px rgba(0, 0, 0, 0.08);
    }

    .positions-table th {
      background: var(--red);
      color: white;
      font-weight: 600;
      border: none;
      padding: 1rem;
    }

    .positions-table td {
      padding: 1rem;
      border-bottom: 1px solid #eee;
      vertical-align: middle;
    }

    .positions-table tbody tr:hover {
      background: rgba(220, 53, 69, 0.05);
    }

    .positions-table tbody tr:last-child td {
      border-bottom: none;
    }

    .position-card {
      transition: all 0.3s ease;
      border: none;
      border-radius: 12px;
      overflow: hidden;
      box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    }

    .position-card:hover {
      transform: translateY(-5px);
      box-shadow: 0 8px 15px rgba(240, 15, 0, 0.15);
      border-top: 3px solid var(--red);
    }

    .position-card .card-body {
      padding: 1.5rem;
    }

    .position-meta {
      font-size: 0.875rem;
      color: var(--text-secondary);
    }

    .position-deadline {
      font-size: 0.875rem;
      color: var(--red);
      font-weight: 500;
    }

</style>
<?= $this->endSection() ?>

<?= $this->section('content') ?>

  <!-- Hero Section -->
  <section id="home" class="gradient-bg text-white pt-5 pb-5" style="margin-top: 76px;">
    <div class="content-wrapper container text-center">
      <h1 class="display-4 fw-bold mb-4">Papua New Guinea's First AI-Powered Recruitment System</h1>
      <p class="fs-4 mb-3 text-white-75">DERS - Revolutionizing Government Recruitment with Artificial Intelligence</p>
      <p class="fs-5 mb-4 text-white-75">Experience the future of public service recruitment with our cutting-edge AI technology</p>

      <!-- AI Innovation Badge -->
      <div class="mb-4">
        <span class="badge bg-yellow text-dark fs-6 px-4 py-2 rounded-pill">
          <i class="fas fa-robot me-2"></i>First AI-Integrated System in PNG
        </span>
      </div>

      <!-- Quick Action Buttons -->
      <div class="d-flex flex-column flex-sm-row gap-3 justify-content-center align-items-center mb-4">
        <a href="<?= base_url('jobs') ?>" class="btn btn-yellow btn-lg px-4 py-3">
          <i class="fas fa-search me-2"></i>Browse AI-Enhanced Positions
        </a>
        <a href="<?= base_url('public/assets/Form_RS_3.2.docx') ?>"
           class="btn btn-outline-light btn-lg px-4 py-3"
           download="Form_RS_3.2.docx">
          <i class="fas fa-download me-2"></i>Download Application Form
        </a>
      </div>
    </div>
  </section>

  <!-- Application Form Download Section -->
  <section class="py-4 bg-white border-bottom">
    <div class="container">
      <div class="row align-items-center">
        <div class="col-md-8">
          <div class="d-flex align-items-center">
            <div class="me-4">
              <i class="fas fa-file-download text-red" style="font-size: 2.5rem;"></i>
            </div>
            <div>
              <h3 class="h5 fw-bold text-navy mb-1">Download Application Form</h3>
              <p class="text-muted mb-0">Get the official RS 3.2 Application Form required for all government position applications</p>
            </div>
          </div>
        </div>
        <div class="col-md-4 text-md-end mt-3 mt-md-0">
          <a href="<?= base_url('public/assets/Form_RS_3.2.docx') ?>"
             class="btn btn-red btn-lg"
             download="Form_RS_3.2.docx">
            <i class="fas fa-download me-2"></i>Download RS 3.2 Form
          </a>
        </div>
      </div>
    </div>
  </section>

  <!-- Featured Positions Section -->
  <section class="py-5 bg-white">
    <div class="container">
      <div class="d-flex justify-content-between align-items-center mb-4">
        <h2 class="h3 fw-bold text-navy mb-0">Government Positions (<?= count($latest_positions) ?>)</h2>
        <div class="d-flex gap-3 align-items-center">
          <!-- View Toggle -->
          <div class="view-toggle d-flex">
            <button type="button" class="btn btn-sm active" id="gridViewBtn" onclick="toggleView('grid')">
              <i class="fas fa-th me-1"></i> Grid
            </button>
            <button type="button" class="btn btn-sm" id="tableViewBtn" onclick="toggleView('table')">
              <i class="fas fa-list me-1"></i> Table
            </button>
          </div>
          <a href="<?= base_url('jobs') ?>" class="btn btn-outline-danger">
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-briefcase me-2" viewBox="0 0 16 16">
              <path d="M6.5 1A1.5 1.5 0 0 0 5 2.5V3H1.5A1.5 1.5 0 0 0 0 4.5v8A1.5 1.5 0 0 0 1.5 14h13a1.5 1.5 0 0 0 1.5-1.5v-8A1.5 1.5 0 0 0 14.5 3H11v-.5A1.5 1.5 0 0 0 9.5 1zm0 1h3a.5.5 0 0 1 .5.5V3H6v-.5a.5.5 0 0 1 .5-.5m1.886 6.914L15 7.151V12.5a.5.5 0 0 1-.5.5h-13a.5.5 0 0 1-.5-.5V7.15l6.614 1.764a1.5 1.5 0 0 0 .772 0M1.5 4h13a.5.5 0 0 1 .5.5v1.616L8.129 7.948a.5.5 0 0 1-.258 0L1 6.116V4.5a.5.5 0 0 1 .5-.5"/>
            </svg>
            View All Positions
          </a>
        </div>
      </div>

      <?php if (empty($latest_positions)): ?>
        <div class="card">
          <div class="card-body text-center py-5">
            <svg xmlns="http://www.w3.org/2000/svg" width="48" height="48" fill="currentColor" class="bi bi-clipboard-x text-muted mb-3" viewBox="0 0 16 16">
              <path d="M6.5 0A1.5 1.5 0 0 0 5 1.5v1A1.5 1.5 0 0 0 6.5 4h3A1.5 1.5 0 0 0 11 2.5v-1A1.5 1.5 0 0 0 9.5 0zm3 1a.5.5 0 0 1 .5.5v1a.5.5 0 0 1-.5.5h-3a.5.5 0 0 1-.5-.5v-1a.5.5 0 0 1 .5-.5z"/>
              <path d="M4 1.5H3a2 2 0 0 0-2 2V14a2 2 0 0 0 2 2h10a2 2 0 0 0 2-2V3.5a2 2 0 0 0-2-2h-1v1A2.5 2.5 0 0 1 9.5 5h-3A2.5 2.5 0 0 1 4 2.5zm4 7.793 1.146-1.147a.5.5 0 1 1 .708.708L8.707 10l1.147 1.146a.5.5 0 0 1-.708.708L8 10.707l-1.146 1.147a.5.5 0 0 1-.708-.708L7.293 10 6.146 8.854a.5.5 0 1 1 .708-.708z"/>
            </svg>
            <h3 class="h4 text-muted mb-2">No Positions Available</h3>
            <p class="text-muted mb-4">Check back later for new government positions.</p>
            <a href="<?= base_url('jobs') ?>" class="btn btn-red">
              Browse All Positions
            </a>
          </div>
        </div>
      <?php else: ?>
        <!-- Grid View -->
        <div class="grid-view active" id="gridView">
          <div class="row g-4">
            <?php foreach ($latest_positions as $position): ?>
              <div class="col-md-6 col-lg-4">
                <div class="position-card card h-100">
                  <div class="card-body">
                    <div class="d-flex justify-content-between align-items-start mb-3">
                      <h3 class="h5 fw-bold text-red mb-0"><?= esc($position['designation'] ?? 'Position Not Specified') ?></h3>
                      <div class="d-flex flex-column gap-1">
                        <span class="badge bg-yellow"><?= esc($position['classification'] ?? 'Not Specified') ?></span>
                        <?php if (isset($position['is_internal'])): ?>
                          <?php if ($position['is_internal'] == 1): ?>
                            <span class="badge bg-warning text-dark">Internal</span>
                          <?php else: ?>
                            <span class="badge bg-primary">External</span>
                          <?php endif; ?>
                        <?php endif; ?>
                      </div>
                    </div>
                    <div class="position-meta mb-2">
                      <div class="mb-1">
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-building me-2" viewBox="0 0 16 16">
                          <path d="M4 2.5a.5.5 0 0 1 .5-.5h7a.5.5 0 0 1 .5.5v10.5a.5.5 0 0 1-.5.5h-7a.5.5 0 0 1-.5-.5V2.5zm1 0v10h6V2.5H5z"/>
                          <path d="M2 2a2 2 0 0 0-2 2v11a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V4a2 2 0 0 0-2-2H2zm13 2v11H1V4a1 1 0 0 1 1-1h12a1 1 0 0 1 1 1z"/>
                        </svg>
                        <?= esc($position['org_name'] ?? 'Organization Not Specified') ?>
                      </div>
                      <div class="mb-1">
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-geo-alt me-2" viewBox="0 0 16 16">
                          <path d="M12.166 8.94c-.524 1.062-1.234 2.12-1.96 3.07A31.493 31.493 0 0 1 8 14.58a31.481 31.481 0 0 1-2.206-2.57c-.726-.95-1.436-2.008-1.96-3.07C3.304 7.867 3 6.862 3 6a5 5 0 0 1 10 0c0 .862-.305 1.867-.834 2.94zM8 16s6-5.686 6-10A6 6 0 0 0 2 6c0 4.314 6 10 6 10z"/>
                          <path d="M8 8a2 2 0 1 1 0-4 2 2 0 0 1 0 4zm0 1a3 3 0 1 0 0-6 3 3 0 0 0 0 6z"/>
                        </svg>
                        <?= esc($position['location'] ?? 'Location Not Specified') ?>
                      </div>
                      <div>
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-cash me-2" viewBox="0 0 16 16">
                          <path d="M8 10a2 2 0 1 0 0-4 2 2 0 0 0 0 4z"/>
                          <path d="M0 4a1 1 0 0 1 1-1h14a1 1 0 0 1 1 1v8a1 1 0 0 1-1 1H1a1 1 0 0 1-1-1V4zm3 0a2 2 0 0 1-2 2v4a2 2 0 0 1 2 2h10a2 2 0 0 1 2-2V6a2 2 0 0 1-2-2H3z"/>
                        </svg>
                        <?php
                        $salary = $position['annual_salary'] ?? '0';
                        // Handle salary ranges like "200,000.00 - 230,000.00"
                        if (strpos($salary, ' - ') !== false) {
                            echo 'K' . esc($salary);
                        } else {
                            // Single numeric value
                            $numericSalary = is_numeric($salary) ? $salary : 0;
                            echo 'K' . number_format($numericSalary);
                        }
                        ?>
                      </div>
                    </div>
                    <div class="position-deadline mb-3">
                      <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-calendar-event me-2" viewBox="0 0 16 16">
                        <path d="M11 6.5a.5.5 0 0 1 .5-.5h1a.5.5 0 0 1 .5.5v1a.5.5 0 0 1-.5.5h-1a.5.5 0 0 1-.5-.5v-1z"/>
                        <path d="M3.5 0a.5.5 0 0 1 .5.5V1h8V.5a.5.5 0 0 1 1 0V1h1a2 2 0 0 1 2 2v11a2 2 0 0 1-2 2H2a2 2 0 0 1-2-2V3a2 2 0 0 1 2-2h1V.5a.5.5 0 0 1 .5-.5zM1 4v10a1 1 0 0 0 1 1h12a1 1 0 0 0 1-1V4H1z"/>
                      </svg>
                      Closes on <?= isset($position['publish_date_to']) ? date('d M Y', strtotime($position['publish_date_to'])) : 'Date Not Specified' ?>
                    </div>
                    <a href="<?= base_url('jobs/view/' . ($position['id'] ?? '1')) ?>" class="btn btn-red w-100">View Details</a>
                  </div>
                </div>
              </div>
            <?php endforeach; ?>
          </div>
        </div>

        <!-- Table View -->
        <div class="table-view" id="tableView">
          <div class="table-responsive positions-table">
            <table class="table table-hover mb-0">
              <thead>
                <tr>
                  <th>Position</th>
                  <th>Organization</th>
                  <th>Location</th>
                  <th>Classification</th>
                  <th>Type</th>
                  <th>Salary</th>
                  <th>Deadline</th>
                  <th>Action</th>
                </tr>
              </thead>
              <tbody>
                <?php foreach ($latest_positions as $position): ?>
                  <tr>
                    <td>
                      <div class="fw-bold text-red"><?= esc($position['designation'] ?? 'Position Not Specified') ?></div>
                      <small class="text-muted"><?= esc($position['advertisement_no'] ?? 'N/A') ?></small>
                    </td>
                    <td><?= esc($position['org_name'] ?? 'Organization Not Specified') ?></td>
                    <td><?= esc($position['location'] ?? 'Location Not Specified') ?></td>
                    <td><span class="badge bg-yellow"><?= esc($position['classification'] ?? 'Not Specified') ?></span></td>
                    <td>
                      <?php if (isset($position['is_internal'])): ?>
                        <?php if ($position['is_internal'] == 1): ?>
                          <span class="badge bg-warning text-dark">Internal</span>
                        <?php else: ?>
                          <span class="badge bg-primary">External</span>
                        <?php endif; ?>
                      <?php else: ?>
                        <span class="badge bg-secondary">N/A</span>
                      <?php endif; ?>
                    </td>
                    <td>
                      <?php
                      $salary = $position['annual_salary'] ?? '0';
                      // Handle salary ranges like "200,000.00 - 230,000.00"
                      if (strpos($salary, ' - ') !== false) {
                          echo 'K' . esc($salary);
                      } else {
                          // Single numeric value
                          $numericSalary = is_numeric($salary) ? $salary : 0;
                          echo 'K' . number_format($numericSalary);
                      }
                      ?>
                    </td>
                    <td><?= isset($position['publish_date_to']) ? date('d M Y', strtotime($position['publish_date_to'])) : 'Date Not Specified' ?></td>
                    <td>
                      <a href="<?= base_url('jobs/view/' . ($position['id'] ?? '1')) ?>" class="btn btn-sm btn-red">View Details</a>
                    </td>
                  </tr>
                <?php endforeach; ?>
              </tbody>
            </table>
          </div>
        </div>
      <?php endif; ?>
    </div>
  </section>

  <!-- Statistics Section -->
  <section class="py-5 bg-light">
    <div class="container">
      <div class="row g-4">
        <div class="col-md-4">
          <div class="stat-card">
            <div class="display-5 fw-bold text-red mb-2"><?= isset($current_openings) ? $current_openings : '0' ?></div>
            <div class="text-muted">Current Openings</div>
          </div>
        </div>
        <div class="col-md-4">
          <div class="stat-card">
            <div class="display-5 fw-bold text-yellow mb-2"><?= isset($total_organizations) ? $total_organizations : '0' ?></div>
            <div class="text-muted">Government Agencies</div>
          </div>
        </div>
        <div class="col-md-4">
          <div class="stat-card">
            <div class="display-5 fw-bold text-gray mb-2">5,000+</div>
            <div class="text-muted">Active Applicants</div>
          </div>
        </div>
      </div>
    </div>
  </section>

  <!-- AI Innovation Section -->
  <section class="py-5 bg-white">
    <div class="container">
      <div class="row align-items-center">
        <div class="col-lg-6">
          <h2 class="display-5 fw-bold mb-4 text-red">First AI-Integrated Recruitment System in PNG</h2>
          <p class="fs-5 mb-4 text-muted">DERS represents a groundbreaking advancement in Papua New Guinea's public service recruitment, introducing artificial intelligence to streamline and enhance the entire selection process.</p>
          <div class="row g-3 mb-4">
            <div class="col-sm-6">
              <div class="d-flex align-items-center">
                <i class="fas fa-robot text-red me-3 fs-4"></i>
                <span class="fw-bold">AI-Powered Analysis</span>
              </div>
            </div>
            <div class="col-sm-6">
              <div class="d-flex align-items-center">
                <i class="fas fa-chart-line text-red me-3 fs-4"></i>
                <span class="fw-bold">Smart Matching</span>
              </div>
            </div>
            <div class="col-sm-6">
              <div class="d-flex align-items-center">
                <i class="fas fa-shield-alt text-red me-3 fs-4"></i>
                <span class="fw-bold">Secure Processing</span>
              </div>
            </div>
            <div class="col-sm-6">
              <div class="d-flex align-items-center">
                <i class="fas fa-clock text-red me-3 fs-4"></i>
                <span class="fw-bold">Faster Results</span>
              </div>
            </div>
          </div>
          <a href="<?= base_url('about') ?>" class="btn btn-red btn-lg">
            <i class="fas fa-info-circle me-2"></i>Learn More About Our AI Technology
          </a>
        </div>
        <div class="col-lg-6 text-center">
          <div class="position-relative">
            <i class="fas fa-brain text-red" style="font-size: 12rem; opacity: 0.1;"></i>
            <div class="position-absolute top-50 start-50 translate-middle">
              <i class="fas fa-microchip text-red" style="font-size: 6rem;"></i>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>

  <!-- Features Section -->
  <section id="features" class="py-5 bg-light">
    <div class="container">
      <h2 class="display-5 fw-bold text-center mb-3">AI-Enhanced Features</h2>
      <p class="text-center text-muted mb-5 mx-auto" style="max-width: 700px;">Experience the power of artificial intelligence in government recruitment with our innovative features.</p>

      <div class="row g-4">
        <div class="col-md-4">
          <div class="card h-100 job-card">
            <div class="card-body p-4 text-center">
              <i class="fas fa-file-alt text-red mb-3" style="font-size: 3rem;"></i>
              <h3 class="h5 fw-bold mb-3 text-navy">AI Document Analysis</h3>
              <p class="text-muted">Our AI automatically extracts and analyzes information from your uploaded documents, creating comprehensive applicant profiles.</p>
            </div>
          </div>
        </div>
        <div class="col-md-4">
          <div class="card h-100 job-card">
            <div class="card-body p-4 text-center">
              <i class="fas fa-search text-red mb-3" style="font-size: 3rem;"></i>
              <h3 class="h5 fw-bold mb-3 text-navy">Intelligent Matching</h3>
              <p class="text-muted">Advanced algorithms match your qualifications with suitable positions, ensuring the best fit for both applicants and organizations.</p>
            </div>
          </div>
        </div>
        <div class="col-md-4">
          <div class="card h-100 job-card">
            <div class="card-body p-4 text-center">
              <i class="fas fa-tachometer-alt text-red mb-3" style="font-size: 3rem;"></i>
              <h3 class="h5 fw-bold mb-3 text-navy">Streamlined Processing</h3>
              <p class="text-muted">AI-powered automation reduces processing time and eliminates manual errors, making recruitment faster and more efficient.</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>



<?= $this->endSection() ?>

<?= $this->section('js') ?>
<script>
function toggleView(viewType) {
    const gridView = document.getElementById('gridView');
    const tableView = document.getElementById('tableView');
    const gridBtn = document.getElementById('gridViewBtn');
    const tableBtn = document.getElementById('tableViewBtn');

    if (viewType === 'grid') {
        // Show grid view
        gridView.classList.add('active');
        tableView.classList.remove('active');

        // Update button states
        gridBtn.classList.add('active');
        tableBtn.classList.remove('active');
    } else if (viewType === 'table') {
        // Show table view
        tableView.classList.add('active');
        gridView.classList.remove('active');

        // Update button states
        tableBtn.classList.add('active');
        gridBtn.classList.remove('active');
    }
}
</script>
<?= $this->endSection() ?>