<?php

namespace App\Controllers;

use App\Models\DakoiiUsersModel;
use App\Models\DakoiiOrgModel;
use App\Models\UsersModel;
use App\Models\GeoProvincesModel;
use App\Models\GeoDistrictsModel;
use App\Models\GeoCountriesModel;
use App\Models\ExerciseModel;

class Dakoii extends BaseController
{
    public $session;
    protected $dusersModel;
    protected $dakoiiOrgModel;
    protected $usersModel;
    protected $provinceModel;
    protected $districtModel;
    protected $countryModel;
    protected $exerciseModel;

    public function __construct()
    {
        helper(['form', 'url', 'info']);
        $this->session = session();
        $this->dusersModel = new DakoiiUsersModel();
        $this->dakoiiOrgModel = new DakoiiOrgModel();
        $this->usersModel = new UsersModel();
        $this->provinceModel = new GeoProvincesModel();
        $this->districtModel = new GeoDistrictsModel();
        $this->countryModel = new GeoCountriesModel();
        $this->exerciseModel = new ExerciseModel();
    }

    // Authentication Methods
    public function index()
    {
        $data['title'] = "Dakoii Admin";
        $data['menu'] = "dlogin";
        echo view('dakoii/dakoii_login', $data);
    }

    public function loginForm()
    {
        $data['title'] = "Dakoii Admin";
        $data['menu'] = "dlogin";
        echo view('dakoii/dakoii_login', $data);
    }

    public function processLogin()
    {
        $rules = [
            'username' => 'required',
            'password' => 'required'
        ];

        if (!$this->validate($rules)) {
            session()->setFlashdata('error', 'Please enter both username and password');
            return redirect()->to('dakoii');
        }

        $username = $this->request->getVar('username');
        $password = $this->request->getVar('password');

        try {
            // Verify user credentials using the model
            $user = $this->dusersModel->verifyUser($username, $password);

            if ($user) {
                // Check if user is active
                if (!$user['is_active']) {
                    session()->setFlashdata('error', 'Your account has been deactivated. Please contact administrator.');
                    return redirect()->to('dakoii');
                }

                // Set session data
                $this->session->set([
                    'logged_in' => true,
                    'user_id' => $user['id'],
                    'name' => $user['name'],
                    'username' => $user['username'],
                    'role' => $user['role'],
                    'orgcode' => $user['orgcode']
                ]);

                session()->setFlashdata('success', 'Welcome back, ' . $user['name']);
                return redirect()->to('dakoii/dashboard');
            } else {
                session()->setFlashdata('error', 'Invalid username or password');
                return redirect()->to('dakoii');
            }
        } catch (\Exception $e) {
            log_message('error', 'Login error: ' . $e->getMessage());
            session()->setFlashdata('error', 'An error occurred during login. Please try again.');
            return redirect()->to('dakoii');
        }
    }

    public function adminLogout()
    {
        $this->session->destroy();
        session()->setFlashdata('success', 'You have been logged out successfully');
        return redirect()->to('dakoii');
    }

    // Dashboard Methods
    public function dashboard()
    {
        $data['title'] = "Dashboard";
        $data['menu'] = "dashboard";

        try {
            // Get real system users (Dakoii users)
            $data['dusers'] = $this->dusersModel->findAll();

            // Get real organization admins (Users table)
            $data['admins'] = $this->usersModel->findAll();

            // Get real organizations
            $organizations = $this->dakoiiOrgModel->findAll();
            $data['org'] = [];
            foreach ($organizations as $org) {
                $data['org'][] = [
                    'id' => $org['id'],
                    'name' => $org['org_name'],
                    'orgcode' => $org['org_code'],
                    'logo_path' => $org['logo_path'] ?? null,
                    'is_active' => $org['is_active'],
                    'license_status' => $org['license_status'] ?? 'pending'
                ];
            }

            // Get real geographic statistics
            $data['provinces_count'] = $this->provinceModel->countAllResults();
            $data['districts_count'] = $this->districtModel->countAllResults();
            $data['llgs_count'] = 0; // LLG tables not available
            $data['wards_count'] = 0; // Ward tables not available

            $data['province_stats'] = $this->getProvinceStats();

            // Get real pending exercises
            $pendingExercises = $this->exerciseModel
                ->where('status', 'publish_request')
                ->findAll();

            $data['pending_exercises'] = [];
            foreach ($pendingExercises as $exercise) {
                // Get organization name for each exercise
                $org = $this->dakoiiOrgModel->find($exercise['org_id']);
                $data['pending_exercises'][] = [
                    'id' => $exercise['id'],
                    'exercise_name' => $exercise['exercise_name'],
                    'status' => $exercise['status'],
                    'org_id' => $exercise['org_id'],
                    'org_name' => $org ? $org['org_name'] : 'Unknown Organization',
                    'publish_date_from' => $exercise['publish_date_from'] ?? null,
                    'publish_date_to' => $exercise['publish_date_to'] ?? null
                ];
            }

            // Education levels - keeping as static for now since it's reference data
            $data['education'] = [
                ['id' => 1, 'name' => 'Primary Education', 'icon' => 'fa-school'],
                ['id' => 2, 'name' => 'Secondary Education', 'icon' => 'fa-graduation-cap']
            ];

        } catch (\Exception $e) {
            log_message('error', 'Dashboard error: ' . $e->getMessage());

            // Fallback to empty arrays if database error occurs
            $data['dusers'] = [];
            $data['admins'] = [];
            $data['org'] = [];
            $data['provinces_count'] = 0;
            $data['districts_count'] = 0;
            $data['llgs_count'] = 0;
            $data['wards_count'] = 0;
            $data['province_stats'] = [];
            $data['pending_exercises'] = [];
            $data['education'] = [];

            session()->setFlashdata('error', 'Error loading dashboard data. Please try again.');
        }

        echo view('dakoii/dakoii_ddash', $data);
    }

    private function getProvinceStats()
    {
        try {
            $provinces = $this->provinceModel->findAll();
            $stats = [];

            foreach ($provinces as $province) {
                $districts = $this->districtModel->where('province_id', $province['id'])->findAll();

                // LLG and Ward counts disabled - tables not available
                $llgs_count = 0;
                $wards_count = 0;

                $stats[$province['id']] = [
                    'name' => $province['name'],
                    'districts' => count($districts),
                    'llgs' => $llgs_count,
                    'wards' => $wards_count
                ];
            }

            return $stats;
        } catch (\Exception $e) {
            log_message('error', 'Province stats error: ' . $e->getMessage());
            return [];
        }
    }

    // Organization Methods
    public function organizationList()
    {
        $data['title'] = "Organizations";
        $data['menu'] = "organizations";

        try {
            $organizationsData = $this->dakoiiOrgModel->findAll();

            // Map database fields to view-expected format
            $data['organizations'] = [];
            foreach ($organizationsData as $org) {
                $data['organizations'][] = [
                    'id' => $org['id'],
                    'name' => $org['org_name'], // Map org_name to name for view
                    'org_name' => $org['org_name'],
                    'org_code' => $org['org_code'],
                    'orgcode' => $org['org_code'], // Alias for compatibility
                    'description' => $org['description'],
                    'location_lock_province' => $org['location_lock_province'],
                    'location_lock_country' => $org['location_lock_country'],
                    'logo_path' => $org['logo_path'],
                    'is_locationlocked' => $org['is_locationlocked'],
                    'postal_address' => $org['postal_address'],
                    'phone_numbers' => $org['phone_numbers'],
                    'email_addresses' => $org['email_addresses'],
                    'is_active' => $org['is_active'],
                    'license_status' => $org['license_status'],
                    'created_at' => $org['created_at'],
                    'updated_at' => $org['updated_at']
                ];
            }
        } catch (\Exception $e) {
            log_message('error', 'Organization list error: ' . $e->getMessage());
            $data['organizations'] = [];
        }

        echo view('dakoii/dakoii_organizations', $data);
    }

    public function organizationCreateForm()
    {
        $data['title'] = "Create Organization";
        $data['menu'] = "organizations";

        // Get countries and provinces for dropdowns
        try {
            $data['countries'] = $this->countryModel->findAll();
            $data['provinces'] = $this->provinceModel->findAll();
        } catch (\Exception $e) {
            log_message('error', 'Organization create form error: ' . $e->getMessage());
            $data['countries'] = [];
            $data['provinces'] = [];
        }

        echo view('dakoii/dakoii_organization_create', $data);
    }

    public function organizationStore()
    {
        // Validate required fields
        $rules = [
            'org_name' => 'required|max_length[255]'
        ];

        if (!$this->validate($rules)) {
            session()->setFlashdata('error', 'Validation failed: ' . implode(', ', $this->validator->getErrors()));
            return redirect()->back()->withInput();
        }

        // Generate unique organization code
        $orgcode = rand(11111, 99999);
        while (!empty($this->dakoiiOrgModel->where('org_code', $orgcode)->first())) {
            $orgcode = rand(11111, 99999);
        }

        // Prepare organization data
        $data = [
            'org_code' => $orgcode,
            'org_name' => $this->request->getVar('org_name'),
            'description' => $this->request->getVar('description'),
            'location_lock_country' => $this->request->getVar('location_lock_country'),
            'location_lock_province' => $this->request->getVar('location_lock_province'),
            'is_locationlocked' => $this->request->getVar('is_locationlocked') ? 1 : 0,
            'postal_address' => $this->request->getVar('postal_address'),
            'phone_numbers' => $this->request->getVar('phone_numbers'),
            'email_addresses' => $this->request->getVar('email_addresses'),
            'is_active' => 1,
            'license_status' => 'trial', // Default to trial
            'created_by' => session()->get('user_id'),
            'updated_by' => session()->get('user_id')
        ];

        // Handle logo upload
        $logoFile = $this->request->getFile('org_logo');
        if ($logoFile && $logoFile->isValid() && $logoFile->getSize() > 0) {
            // Make sure the upload directory exists
            $uploadPath = ROOTPATH . 'public/uploads/org_logo/';
            if (!is_dir($uploadPath)) {
                mkdir($uploadPath, 0777, true);
            }

            $newName = $orgcode . "_" . time() . '.' . $logoFile->getExtension();
            $logoFile->move($uploadPath, $newName);

            // Store the relative path
            $data['logo_path'] = 'public/uploads/org_logo/' . $newName;

            // Log the upload for debugging
            log_message('info', 'Organization logo uploaded: ' . $data['logo_path']);
        }

        // Insert organization
        if ($this->dakoiiOrgModel->insert($data)) {
            session()->setFlashdata('success', 'Organization Created Successfully');
            return redirect()->to('dakoii/organization/view/' . $orgcode);
        } else {
            session()->setFlashdata('error', 'Failed to create organization');
            return redirect()->back()->withInput();
        }
    }

    public function organizationView($orgcode)
    {
        $organization = $this->dakoiiOrgModel->where('org_code', $orgcode)->first();
        if (empty($organization)) {
            return redirect()->to(base_url('dakoii/dashboard'));
        }

        // Debug: Log the organization data to see what's retrieved
        log_message('debug', 'Organization data for orgcode ' . $orgcode . ': ' . json_encode($organization));

        // Map database fields to view-expected format
        $org = [
            'id' => $organization['id'],
            'name' => $organization['org_name'], // Map org_name to name for view
            'org_name' => $organization['org_name'],
            'org_code' => $organization['org_code'],
            'orgcode' => $organization['org_code'], // Alias for compatibility
            'description' => $organization['description'],
            'location_lock_province' => $organization['location_lock_province'],
            'location_lock_country' => $organization['location_lock_country'],
            'logo_path' => $organization['logo_path'],
            'is_locationlocked' => $organization['is_locationlocked'],
            'postal_address' => $organization['postal_address'],
            'phone_numbers' => $organization['phone_numbers'],
            'email_addresses' => $organization['email_addresses'],
            'is_active' => $organization['is_active'],
            'license_status' => $organization['license_status'],
            'created_at' => $organization['created_at'],
            'updated_at' => $organization['updated_at']
        ];

        // Debug: Log the mapped org data
        log_message('debug', 'Mapped org data: ' . json_encode($org));

        $data['title'] = "Organization Details";
        $data['menu'] = "organizations";
        $data['org'] = $org;

        // Get organization admins with error handling
        try {
            $data['admins'] = $this->usersModel->where('orgcode', $orgcode)->findAll();
        } catch (\Exception $e) {
            log_message('error', 'Error fetching admins for organization ' . $orgcode . ': ' . $e->getMessage());
            $data['admins'] = [];
        }

        // Get the province and country
        if (!empty($org['location_lock_country'])) {
            $country = $this->countryModel->find($org['location_lock_country']);
            if (!empty($country)) {
                $data['country_name'] = $country['name'];
            }
        }

        if (!empty($org['location_lock_province'])) {
            $province = $this->provinceModel->find($org['location_lock_province']);
            if (!empty($province)) {
                $data['province_name'] = $province['name'];
            }
        }

        // Get all provinces for the set country
        $data['set_country'] = $this->countryModel->findAll()[0];
        $data['get_provinces'] = $this->provinceModel->findAll();

        // Get exercises for this organization
        $data['exercises'] = $this->exerciseModel->where('org_id', $org['id'])->findAll();

        // Check if the user has permission to add exercises
        $data['can_add_exercise'] = true; // Default to true, adjust based on your permission system

        echo view('dakoii/dakoii_open_org', $data);
    }

    public function organizationEditForm($orgcode)
    {
        $organization = $this->dakoiiOrgModel->where('org_code', $orgcode)->first();
        if (empty($organization)) {
            return redirect()->to(base_url('dakoii/dashboard'));
        }

        // Map database fields to view-expected format
        $org = [
            'id' => $organization['id'],
            'name' => $organization['org_name'], // Map org_name to name for view
            'org_name' => $organization['org_name'],
            'org_code' => $organization['org_code'],
            'orgcode' => $organization['org_code'], // Alias for compatibility
            'description' => $organization['description'],
            'location_lock_province' => $organization['location_lock_province'],
            'location_lock_country' => $organization['location_lock_country'],
            'logo_path' => $organization['logo_path'],
            'is_locationlocked' => $organization['is_locationlocked'],
            'postal_address' => $organization['postal_address'],
            'phone_numbers' => $organization['phone_numbers'],
            'email_addresses' => $organization['email_addresses'],
            'is_active' => $organization['is_active'],
            'license_status' => $organization['license_status'],
            'created_at' => $organization['created_at'],
            'updated_at' => $organization['updated_at']
        ];

        $data['title'] = "Edit Organization";
        $data['menu'] = "organizations";
        $data['org'] = $org;

        // Get the province and country
        if (!empty($org['location_lock_country'])) {
            $country = $this->countryModel->find($org['location_lock_country']);
            if (!empty($country)) {
                $data['country_name'] = $country['name'];
            }
        }

        if (!empty($org['location_lock_province'])) {
            $province = $this->provinceModel->find($org['location_lock_province']);
            if (!empty($province)) {
                $data['province_name'] = $province['name'];
            }
        }

        // Get all countries and provinces for dropdowns
        try {
            $data['countries'] = $this->countryModel->findAll();
            $data['get_provinces'] = $this->provinceModel->findAll();
            $data['set_country'] = !empty($data['countries']) ? $data['countries'][0] : ['id' => 1, 'name' => 'Default Country'];
        } catch (\Exception $e) {
            log_message('error', 'Organization edit form error: ' . $e->getMessage());
            $data['countries'] = [];
            $data['get_provinces'] = [];
            $data['set_country'] = ['id' => 1, 'name' => 'Default Country'];
        }

        echo view('dakoii/dakoii_organization_edit', $data);
    }

    public function organizationUpdate($orgcode)
    {
        // Validate organization exists
        $organization = $this->dakoiiOrgModel->where('org_code', $orgcode)->first();
        if (empty($organization)) {
            session()->setFlashdata('error', 'Organization not found');
            return redirect()->to('dakoii/organization/list');
        }

        // Validate required fields
        if (!$this->validate(['org_name' => 'required'])) {
            session()->setFlashdata('error', 'Organization name is required');
            return redirect()->back()->withInput();
        }

        $id = $organization['id'];

        // Handle province selection
        $addprov = "";
        if (!empty($this->request->getVar('country'))) {
            $addprov = $this->request->getVar('province');
        }

        // Prepare update data
        $data = [
            'org_name' => $this->request->getVar('org_name'),
            'description' => $this->request->getVar('description'),
            'location_lock_country' => $this->request->getVar('country'),
            'location_lock_province' => $addprov,
            'is_locationlocked' => $this->request->getVar('is_locationlocked') ? 1 : 0,
            'postal_address' => $this->request->getVar('postal_address'),
            'phone_numbers' => $this->request->getVar('phone_numbers'),
            'email_addresses' => $this->request->getVar('email_addresses'),
            'is_active' => $this->request->getVar('status'),
            'updated_by' => session()->get('user_id')
        ];

        // Process logo upload
        $logoFile = $this->request->getFile('org_logo');
        if ($logoFile && $logoFile->isValid() && $logoFile->getSize() > 0) {
            // Make sure the upload directory exists
            $uploadPath = ROOTPATH . 'public/uploads/org_logo/';
            if (!is_dir($uploadPath)) {
                mkdir($uploadPath, 0777, true);
            }

            // Generate unique name and move the file
            $newName = $orgcode . "_" . time() . '.' . $logoFile->getExtension();
            $logoFile->move($uploadPath, $newName);

            // Set the relative path for storage
            $data['logo_path'] = 'public/uploads/org_logo/' . $newName;

            // Log the upload for debugging
            log_message('info', 'Organization logo uploaded: ' . $data['logo_path']);
        }

        // Update organization
        if ($this->dakoiiOrgModel->update($id, $data)) {
            session()->setFlashdata('success', 'Organization Updated Successfully');
        } else {
            session()->setFlashdata('error', 'Failed to update organization');
        }

        return redirect()->to('dakoii/organization/view/'.$orgcode);
    }

    public function organizationLicenseEditForm($orgcode)
    {
        $organization = $this->dakoiiOrgModel->where('org_code', $orgcode)->first();
        if (empty($organization)) {
            session()->setFlashdata('error', 'Organization not found');
            return redirect()->to(base_url('dakoii/dashboard'));
        }

        // Map database fields to view-expected format
        $org = [
            'id' => $organization['id'],
            'name' => $organization['org_name'],
            'org_name' => $organization['org_name'],
            'org_code' => $organization['org_code'],
            'orgcode' => $organization['org_code'],
            'license_status' => $organization['license_status'],
        ];

        $data['title'] = "Edit License Status";
        $data['menu'] = "organizations";
        $data['org'] = $org;

        echo view('dakoii/dakoii_organization_license_edit', $data);
    }

    public function organizationLicenseUpdate($orgcode)
    {
        // Validate organization exists
        $organization = $this->dakoiiOrgModel->where('org_code', $orgcode)->first();
        if (empty($organization)) {
            session()->setFlashdata('error', 'Organization not found');
            return redirect()->to('dakoii/organization/list');
        }

        if (!$this->validate(['license_status' => 'required'])) {
            session()->setFlashdata('error', 'License status is required');
            return redirect()->back()->withInput();
        }

        $data = [
            'license_status' => $this->request->getVar('license_status'),
            'updated_by' => session()->get('user_id')
        ];

        if ($this->dakoiiOrgModel->update($organization['id'], $data)) {
            session()->setFlashdata('success', 'License Status Updated Successfully');
        } else {
            session()->setFlashdata('error', 'Failed to update license status');
        }

        return redirect()->to('dakoii/organization/view/'.$orgcode);
    }

    public function organizationAdminEditForm($orgcode, $adminId)
    {
        $organization = $this->dakoiiOrgModel->where('org_code', $orgcode)->first();
        if (empty($organization)) {
            session()->setFlashdata('error', 'Organization not found');
            return redirect()->to(base_url('dakoii/dashboard'));
        }

        $admin = $this->usersModel->find($adminId);
        if (empty($admin) || $admin['orgcode'] !== $orgcode) {
            session()->setFlashdata('error', 'Admin not found or does not belong to this organization');
            return redirect()->to('dakoii/organization/view/'.$orgcode);
        }

        // Map database fields to view-expected format
        $org = [
            'id' => $organization['id'],
            'name' => $organization['org_name'],
            'org_name' => $organization['org_name'],
            'org_code' => $organization['org_code'],
            'orgcode' => $organization['org_code'],
        ];

        $data['title'] = "Edit Organization Admin";
        $data['menu'] = "organizations";
        $data['org'] = $org;
        $data['admin'] = $admin;

        echo view('dakoii/dakoii_organization_admin_edit', $data);
    }

    // User Management Methods
    public function systemUserCreateForm()
    {
        $data['title'] = "Create System User";
        $data['menu'] = "users";
        echo view('dakoii/dakoii_system_user_create', $data);
    }

    public function systemUserStore()
    {
        if (!$this->validate([
            'username' => 'required|is_unique[dakoii_users.username]',
            'password' => 'required',
            'name' => 'required',
            'role' => 'required'
        ])) {
            session()->setFlashdata('error', 'Username already exist or validation failed');
            return redirect()->to('dakoii/dashboard');
        }

        $is_active = !empty($this->request->getVar('is_active')) ? 1 : 0;

        $data = [
            'name' => $this->request->getVar('name'),
            'username' => $this->request->getVar('username'),
            'password' => $this->request->getVar('password'), // Model will hash this automatically
            'role' => $this->request->getVar('role'),
            'orgcode' => 'SYSTEM', // Default orgcode for system users
            'is_active' => $is_active,
            'created_by' => session()->get('user_id'),
            'updated_by' => session()->get('user_id')
        ];

        if ($this->dusersModel->insert($data)) {
            session()->setFlashdata('success', 'System user created successfully');
        } else {
            session()->setFlashdata('error', 'Failed to create system user');
        }
        return redirect()->to('dakoii/dashboard');
    }

    public function organizationAdminCreateForm($orgcode)
    {
        $organization = $this->dakoiiOrgModel->where('org_code', $orgcode)->first();
        if (!$organization) {
            session()->setFlashdata('error', 'Invalid organization');
            return redirect()->to('dakoii/dashboard');
        }

        // Map database fields to view-expected format
        $org = [
            'id' => $organization['id'],
            'name' => $organization['org_name'],
            'org_name' => $organization['org_name'],
            'org_code' => $organization['org_code'],
            'orgcode' => $organization['org_code'], // Alias for compatibility
            'description' => $organization['description'],
            'is_active' => $organization['is_active'],
            'license_status' => $organization['license_status']
        ];

        $data['title'] = "Create Organization Admin";
        $data['menu'] = "organizations";
        $data['org'] = $org;
        echo view('dakoii/dakoii_organization_admin_create', $data);
    }

    public function organizationAdminStore()
    {
        $orgcode = $this->request->getVar('orgcode');

        // Custom validation rules for admin creation
        $rules = [
            'name' => 'required|max_length[255]',
            'username' => 'required|max_length[255]|is_unique[users.username]',
            'password' => 'required|min_length[4]',
            'role' => 'required|in_list[admin,supervisor,user,guest]'
        ];

        if (!$this->validate($rules)) {
            session()->setFlashdata('error', 'Validation failed: ' . implode(', ', $this->validator->getErrors()));
            return redirect()->to('dakoii/organization/view/'.$orgcode);
        }

        $status = !empty($this->request->getVar('is_active')) ? 1 : 0;

        // Get organization details
        $org = $this->dakoiiOrgModel->where('org_code', $orgcode)->first();
        if (!$org) {
            session()->setFlashdata('error', 'Invalid organization');
            return redirect()->back();
        }

        $data = [
            'org_id' => $org['id'],
            'orgcode' => $orgcode,
            'fileno' => '', // Add empty fileno to satisfy model requirements
            'name' => $this->request->getVar('name'),
            'username' => $this->request->getVar('username'),
            'password' => $this->request->getVar('password'),
            'role' => $this->request->getVar('role'),
            'position' => '', // Add empty position
            'id_photo' => '', // Add empty id_photo
            'phone' => '', // Add empty phone
            'email' => '', // Add empty email
            'status' => $status,
            'created_by' => session()->get('user_id'),
            'updated_by' => session()->get('user_id')
        ];

        // Temporarily disable model validation for this insert
        $this->usersModel->skipValidation(true);

        // Log the data being inserted for debugging
        log_message('info', 'Creating admin with data: ' . json_encode($data));

        try {
            $result = $this->usersModel->insert($data);
            if ($result) {
                session()->setFlashdata('success', 'Organization Admin Created');
                log_message('info', 'Admin creation successful');
            } else {
                $errors = $this->usersModel->errors();
                $errorMessage = !empty($errors) ? implode(', ', $errors) : 'Unknown database error';
                session()->setFlashdata('error', 'Failed to create admin: ' . $errorMessage);
                log_message('error', 'Admin creation failed - Error: ' . $errorMessage);
            }
        } catch (\Exception $e) {
            session()->setFlashdata('error', 'Database error: ' . $e->getMessage());
            log_message('error', 'Admin creation exception - Exception: ' . $e->getMessage());
        }

        // Re-enable model validation
        $this->usersModel->skipValidation(false);

        return redirect()->to('dakoii/organization/view/'.$orgcode);
    }

    public function organizationAdminUpdate($orgcode)
    {
        $id = $this->request->getVar('id');

        // Get organization details
        $org = $this->dakoiiOrgModel->where('org_code', $orgcode)->first();
        if (!$org) {
            session()->setFlashdata('error', 'Invalid organization');
            return redirect()->to('dakoii/organization/list');
        }

        // Custom validation rules for admin update
        $rules = [
            'name' => 'required|max_length[255]',
            'username' => 'required|max_length[255]|is_unique[users.username,id,' . $id . ']',
            'role' => 'required|in_list[admin,supervisor,user,guest]',
            'password' => 'permit_empty|min_length[4]'
        ];

        if ($this->validate($rules)) {
            $data = [
                'org_id' => $org['id'],
                'orgcode' => $orgcode,
                'fileno' => '', // Add empty fileno to satisfy model requirements
                'name' => $this->request->getVar('name'),
                'username' => $this->request->getVar('username'),
                'role' => $this->request->getVar('role'),
                'position' => '', // Add empty position
                'id_photo' => '', // Add empty id_photo
                'phone' => '', // Add empty phone
                'email' => '', // Add empty email
                'status' => $this->request->getVar('is_active') ? 1 : 0,
                'updated_by' => session()->get('user_id')
            ];

            // Handle password update
            $password = $this->request->getVar('password');
            if (!empty($password)) {
                $data['password'] = $password;
            }

            // Temporarily disable model validation for this update
            $this->usersModel->skipValidation(true);

            // Log the data being updated for debugging
            log_message('info', 'Updating admin ID: ' . $id . ' with data: ' . json_encode($data));

            try {
                $result = $this->usersModel->update($id, $data);
                if ($result) {
                    session()->setFlashdata('success', 'Admin updated successfully');
                    log_message('info', 'Admin update successful for ID: ' . $id);
                } else {
                    $errors = $this->usersModel->errors();
                    $errorMessage = !empty($errors) ? implode(', ', $errors) : 'Unknown database error';
                    session()->setFlashdata('error', 'Failed to update admin: ' . $errorMessage);
                    log_message('error', 'Admin update failed for ID: ' . $id . ' - Error: ' . $errorMessage);
                }
            } catch (\Exception $e) {
                session()->setFlashdata('error', 'Database error: ' . $e->getMessage());
                log_message('error', 'Admin update exception for ID: ' . $id . ' - Exception: ' . $e->getMessage());
            }

            // Re-enable model validation
            $this->usersModel->skipValidation(false);
        } else {
            session()->setFlashdata('error', 'Validation failed: ' . implode(', ', $this->validator->getErrors()));
        }
        return redirect()->to('dakoii/organization/view/'.$orgcode);
    }

    public function systemUserUpdate()
    {
        $id = $this->request->getPost('id');

        $rules = [
            'name' => 'required',
            'username' => 'required|is_unique[dakoii_users.username,id,' . $id . ']',
            'role' => 'required'
        ];

        if ($this->validate($rules)) {
            $data = [
                'name' => $this->request->getPost('name'),
                'username' => $this->request->getPost('username'),
                'role' => $this->request->getPost('role'),
                'is_active' => $this->request->getPost('is_active') ? 1 : 0,
                'updated_by' => session()->get('user_id')
            ];

            if (!empty($this->request->getPost('password'))) {
                $data['password'] = $this->request->getPost('password'); // Model will hash this automatically
            }

            if ($this->dusersModel->update($id, $data)) {
                session()->setFlashdata('success', 'System user updated successfully');
            } else {
                session()->setFlashdata('error', 'Failed to update system user');
            }
        } else {
            session()->setFlashdata('error', 'Validation failed: ' . implode(', ', $this->validator->getErrors()));
        }
        return redirect()->to('dakoii/dashboard');
    }

    // Province Management Methods - RESTful CRUD

    /**
     * Display list of provinces (GET)
     */
    public function provinceList()
    {
        $data['title'] = "Provinces";
        $data['menu'] = "provinces";

        try {
            // Get the default country (assuming Papua New Guinea)
            $data['set_country'] = $this->countryModel->where('country_code', 'PG')->first();

            // If no country found, get the first country
            if (empty($data['set_country'])) {
                $data['set_country'] = $this->countryModel->first();
            }

            if (!empty($data['set_country'])) {
                $provincesData = $this->provinceModel
                    ->where('country_id', $data['set_country']['id'])
                    ->orderBy('name', 'asc')
                    ->findAll();

                // Map database fields to view-expected format
                $data['provinces'] = [];
                foreach ($provincesData as $province) {
                    $data['provinces'][] = [
                        'id' => $province['id'],
                        'name' => $province['name'],
                        'provincecode' => $province['province_code'], // Map province_code to provincecode for view
                        'province_code' => $province['province_code'],
                        'country_id' => $province['country_id'],
                        'json_id' => $province['json_id'] ?? '',
                        'created_at' => $province['created_at'],
                        'updated_at' => $province['updated_at']
                    ];
                }
            } else {
                $data['provinces'] = [];
            }
        } catch (\Exception $e) {
            log_message('error', 'Province list error: ' . $e->getMessage());
            $data['provinces'] = [];
            $data['set_country'] = ['id' => 1, 'name' => 'Default Country'];
        }

        echo view('dakoii/dakoii_provinces', $data);
    }

    /**
     * Show form to create new province (GET)
     */
    public function provinceCreateForm()
    {
        $data['title'] = "Create Province";
        $data['menu'] = "provinces";

        // Get countries for dropdown
        $data['countries'] = $this->countryModel->findAll();
        $data['set_country'] = $this->countryModel->where('country_code', 'PG')->first();

        if (empty($data['set_country'])) {
            $data['set_country'] = $this->countryModel->first();
        }

        echo view('dakoii/dakoii_province_create', $data);
    }

    /**
     * Create new province (POST)
     */
    public function provinceCreate()
    {
        $rules = [
            'name' => 'required|min_length[3]|max_length[100]',
            'provincecode' => 'required|max_length[10]', // Unique check will be handled by the model
            'country_id' => 'required|numeric'
        ];

        if (!$this->validate($rules)) {
            session()->setFlashdata('error', 'Validation failed: ' . implode(', ', $this->validator->getErrors()));
            return redirect()->to('dakoii/province/list');
        }

        try {
            $data = [
                'name' => $this->request->getPost('name'),
                'province_code' => $this->request->getPost('provincecode'), // Map provincecode to province_code
                'country_id' => $this->request->getPost('country_id'),
                'json_id' => $this->request->getPost('json_id'),
                'created_by' => session()->get('user_id'),
                'updated_by' => session()->get('user_id')
            ];

            if ($this->provinceModel->insert($data)) {
                session()->setFlashdata('success', 'Province "' . $data['name'] . '" has been added successfully');
            } else {
                // Get model errors if available
                $errors = $this->provinceModel->errors();
                if (!empty($errors)) {
                    session()->setFlashdata('error', 'Validation failed: ' . implode(', ', $errors));
                } else {
                    session()->setFlashdata('error', 'Failed to add province. Please try again.');
                }
            }
        } catch (\Exception $e) {
            log_message('error', 'Province create error: ' . $e->getMessage());
            session()->setFlashdata('error', 'An error occurred while creating the province: ' . $e->getMessage());
        }

        return redirect()->to('dakoii/province/list');
    }

    /**
     * Show form to edit province (GET)
     */
    public function provinceEditForm($id)
    {
        $data['title'] = "Edit Province";
        $data['menu'] = "provinces";

        try {
            $provinceData = $this->provinceModel->find($id);
            if (empty($provinceData)) {
                session()->setFlashdata('error', 'Province not found');
                return redirect()->to('dakoii/province/list');
            }

            // Map database fields to view-expected format
            $data['province'] = [
                'id' => $provinceData['id'],
                'name' => $provinceData['name'],
                'provincecode' => $provinceData['province_code'], // Map province_code to provincecode for view
                'province_code' => $provinceData['province_code'],
                'country_id' => $provinceData['country_id'],
                'json_id' => $provinceData['json_id'] ?? '',
                'created_at' => $provinceData['created_at'],
                'updated_at' => $provinceData['updated_at']
            ];

            // Get countries for dropdown
            $data['countries'] = $this->countryModel->findAll();
            $data['set_country'] = $this->countryModel->find($provinceData['country_id']);

        } catch (\Exception $e) {
            log_message('error', 'Province edit form error: ' . $e->getMessage());
            session()->setFlashdata('error', 'Error loading province data');
            return redirect()->to('dakoii/province/list');
        }

        echo view('dakoii/dakoii_province_edit', $data);
    }

    /**
     * Update province (POST)
     */
    public function provinceUpdate()
    {
        $id = $this->request->getPost('id');

        // Basic validation - unique check will be handled by the model
        $rules = [
            'name' => 'required|min_length[3]|max_length[100]',
            'provincecode' => 'required|max_length[10]',
            'country_id' => 'required|numeric'
        ];

        if (!$this->validate($rules)) {
            session()->setFlashdata('error', 'Validation failed: ' . implode(', ', $this->validator->getErrors()));
            return redirect()->to('dakoii/province/list');
        }

        try {
            // Get the existing province to check if it exists
            $existingProvince = $this->provinceModel->find($id);
            if (empty($existingProvince)) {
                session()->setFlashdata('error', 'Province not found');
                return redirect()->to('dakoii/province/list');
            }

            // Prepare data for update
            $data = [
                'name' => $this->request->getPost('name'),
                'province_code' => $this->request->getPost('provincecode'), // Map provincecode to province_code
                'country_id' => $this->request->getPost('country_id'),
                'json_id' => $this->request->getPost('json_id'),
                'updated_by' => session()->get('user_id')
            ];

            // Perform the update (no need for manual duplicate check since validation handles it)
            if ($this->provinceModel->update($id, $data)) {
                session()->setFlashdata('success', 'Province "' . $data['name'] . '" updated successfully');
            } else {
                // Get model errors if available
                $errors = $this->provinceModel->errors();
                if (!empty($errors)) {
                    session()->setFlashdata('error', 'Model validation failed: ' . implode(', ', $errors));
                } else {
                    session()->setFlashdata('error', 'Failed to update province');
                }
            }
        } catch (\Exception $e) {
            log_message('error', 'Province update error: ' . $e->getMessage());
            session()->setFlashdata('error', 'An error occurred while updating the province: ' . $e->getMessage());
        }

        return redirect()->to('dakoii/province/list');
    }

    /**
     * Delete province (GET)
     */
    public function provinceDelete($id)
    {
        try {
            $province = $this->provinceModel->find($id);
            if (empty($province)) {
                session()->setFlashdata('error', 'Province not found');
                return redirect()->to('dakoii/province/list');
            }

            if ($this->provinceModel->delete($id)) {
                session()->setFlashdata('success', 'Province "' . $province['name'] . '" deleted successfully');
            } else {
                session()->setFlashdata('error', 'Failed to delete province');
            }
        } catch (\Exception $e) {
            log_message('error', 'Province delete error: ' . $e->getMessage());
            session()->setFlashdata('error', 'An error occurred while deleting the province.');
        }
        return redirect()->to('dakoii/province/list');
    }

    /**
     * Get province data as JSON (GET)
     */
    public function provinceGet($id)
    {
        try {
            $provinceData = $this->provinceModel->find($id);
            if (empty($provinceData)) {
                return $this->response->setJSON(['error' => 'Province not found'], 404);
            }

            // Map database fields to view-expected format
            $province = [
                'id' => $provinceData['id'],
                'name' => $provinceData['name'],
                'provincecode' => $provinceData['province_code'], // Map province_code to provincecode for view
                'province_code' => $provinceData['province_code'],
                'country_id' => $provinceData['country_id'],
                'json_id' => $provinceData['json_id'] ?? '',
                'created_at' => $provinceData['created_at'],
                'updated_at' => $provinceData['updated_at']
            ];

            return $this->response->setJSON($province);
        } catch (\Exception $e) {
            log_message('error', 'Province get error: ' . $e->getMessage());
            return $this->response->setJSON(['error' => 'Internal server error'], 500);
        }
    }

    // District Management Methods
    public function districtList($provinceId)
    {
        $data['title'] = "Districts";
        $data['menu'] = "provinces";

        $provinceData = $this->provinceModel->find($provinceId);
        if (empty($provinceData)) {
            return redirect()->to('dakoii/province/list');
        }

        // Map province fields for view compatibility
        $data['province'] = [
            'id' => $provinceData['id'],
            'name' => $provinceData['name'],
            'provincecode' => $provinceData['province_code'], // Map province_code to provincecode for view
            'province_code' => $provinceData['province_code'],
            'country_id' => $provinceData['country_id'],
            'json_id' => $provinceData['json_id'],
            'created_at' => $provinceData['created_at'],
            'updated_at' => $provinceData['updated_at']
        ];

        $districtsData = $this->districtModel
            ->where('province_id', $provinceId)
            ->orderBy('name', 'asc')
            ->findAll();

        // Map district fields for view compatibility
        $data['districts'] = [];
        foreach ($districtsData as $district) {
            $data['districts'][] = [
                'id' => $district['id'],
                'name' => $district['name'],
                'districtcode' => $district['district_code'], // Map district_code to districtcode for view
                'district_code' => $district['district_code'],
                'province_id' => $district['province_id'],
                'country_id' => $district['country_id'],
                'json_id' => $district['json_id'],
                'created_at' => $district['created_at'],
                'updated_at' => $district['updated_at']
            ];
        }

        echo view('dakoii/dakoii_districts', $data);
    }

    public function districtGetByProvince($provinceId)
    {
        $districts = $this->districtModel
            ->where('province_id', $provinceId)
            ->orderBy('name', 'asc')
            ->findAll();

        return $this->response->setJSON($districts);
    }

    public function districtCreate()
    {
        $rules = [
            'name' => 'required|min_length[3]|max_length[100]',
            'district_code' => 'permit_empty|max_length[10]',
            'province_id' => 'required|numeric',
            'country_id' => 'required|numeric'
        ];

        if ($this->validate($rules)) {
            $data = [
                'name' => $this->request->getPost('name'),
                'district_code' => $this->request->getPost('district_code'),
                'province_id' => $this->request->getPost('province_id'),
                'country_id' => $this->request->getPost('country_id'),
                'json_id' => $this->request->getPost('json_id'),
                'created_by' => session()->get('user_id'),
                'updated_by' => session()->get('user_id')
            ];

            if ($this->districtModel->insert($data)) {
                session()->setFlashdata('success', 'District "' . $data['name'] . '" has been added successfully');
            } else {
                session()->setFlashdata('error', 'Failed to add district. Please try again.');
            }
        } else {
            session()->setFlashdata('error', 'Validation failed: ' . implode(', ', $this->validator->getErrors()));
        }
        return redirect()->back();
    }

    public function districtUpdate()
    {
        $id = $this->request->getPost('id');

        $rules = [
            'name' => 'required|min_length[3]|max_length[100]',
            'district_code' => 'permit_empty|max_length[10]'
        ];

        if ($this->validate($rules)) {
            $data = [
                'name' => $this->request->getPost('name'),
                'district_code' => $this->request->getPost('district_code'),
                'json_id' => $this->request->getPost('json_id'),
                'updated_by' => session()->get('user_id')
            ];

            if ($this->districtModel->update($id, $data)) {
                session()->setFlashdata('success', 'District updated successfully');
            } else {
                session()->setFlashdata('error', 'Failed to update district');
            }
        } else {
            session()->setFlashdata('error', 'Validation failed: ' . implode(', ', $this->validator->getErrors()));
        }
        return redirect()->back();
    }

    public function districtDelete($id)
    {
        $district = $this->districtModel->find($id);
        if ($district) {
            if ($this->districtModel->delete($id)) {
                session()->setFlashdata('success', 'District "' . $district['name'] . '" has been deleted successfully');
            } else {
                session()->setFlashdata('error', 'Failed to delete district. Please try again.');
            }
        } else {
            session()->setFlashdata('error', 'District not found');
        }
        return redirect()->back();
    }

    // LLG Management Methods - DISABLED (Tables not available)
    /*
    public function llgList($districtId)
    {
        $data['title'] = "Local Level Governments";
        $data['menu'] = "provinces";

        $data['district'] = $this->districtModel->find($districtId);
        if (empty($data['district'])) {
            return redirect()->to('dakoii/provinces');
        }

        $data['province'] = $this->provinceModel->find($data['district']['province_id']);

        $data['llgs'] = $this->llgModel
            ->where('district_id', $districtId)
            ->orderBy('name', 'asc')
            ->findAll();

        echo view('dakoii/dakoii_llgs', $data);
    }

    public function llgCreate()
    {
        $rules = [
            'name' => 'required|min_length[3]|max_length[100]',
            'llgcode' => 'required|is_unique[adx_llg.llgcode]',
            'district_id' => 'required|numeric',
            'province_id' => 'required|numeric',
            'country_id' => 'required|numeric'
        ];

        if ($this->validate($rules)) {
            $data = [
                'name' => $this->request->getPost('name'),
                'llgcode' => $this->request->getPost('llgcode'),
                'district_id' => $this->request->getPost('district_id'),
                'province_id' => $this->request->getPost('province_id'),
                'country_id' => $this->request->getPost('country_id'),
                'json_id' => $this->request->getPost('json_id')
            ];

            if ($this->llgModel->insert($data)) {
                session()->setFlashdata('success', 'LLG "' . $data['name'] . '" has been added successfully');
            } else {
                session()->setFlashdata('error', 'Failed to add LLG. Please try again.');
            }
        } else {
            session()->setFlashdata('error', 'Validation failed: ' . implode(', ', $this->validator->getErrors()));
        }
        return redirect()->back();
    }

    public function llgUpdate()
    {
        $id = $this->request->getPost('id');

        $rules = [
            'name' => 'required|min_length[3]|max_length[100]',
            'llgcode' => 'required|is_unique[adx_llg.llgcode,id,' . $id . ']'
        ];

        if ($this->validate($rules)) {
            $data = [
                'name' => $this->request->getPost('name'),
                'llgcode' => $this->request->getPost('llgcode'),
                'json_id' => $this->request->getPost('json_id')
            ];

            if ($this->llgModel->update($id, $data)) {
                session()->setFlashdata('success', 'LLG updated successfully');
            } else {
                session()->setFlashdata('error', 'Failed to update LLG');
            }
        } else {
            session()->setFlashdata('error', 'Validation failed: ' . implode(', ', $this->validator->getErrors()));
        }
        return redirect()->back();
    }

    public function llgDelete($id)
    {
        $llg = $this->llgModel->find($id);
        if ($llg) {
            if ($this->llgModel->delete($id)) {
                session()->setFlashdata('success', 'LLG "' . $llg['name'] . '" has been deleted successfully');
            } else {
                session()->setFlashdata('error', 'Failed to delete LLG. Please try again.');
            }
        } else {
            session()->setFlashdata('error', 'LLG not found');
        }
        return redirect()->back();
    }
    */

    // Ward Management Methods - DISABLED (Tables not available)
    /*
    public function wardList($llgId)
    {
        $data['title'] = "Wards";
        $data['menu'] = "provinces";

        $data['llg'] = $this->llgModel->find($llgId);
        if (empty($data['llg'])) {
            return redirect()->to('dakoii/provinces');
        }

        $data['district'] = $this->districtModel->find($data['llg']['district_id']);
        $data['province'] = $this->provinceModel->find($data['district']['province_id']);

        $data['wards'] = $this->wardModel
            ->where('llg_id', $llgId)
            ->orderBy('name', 'asc')
            ->findAll();

        echo view('dakoii/dakoii_wards', $data);
    }

    public function wardCreate()
    {
        $rules = [
            'name' => 'required|min_length[3]|max_length[100]',
            'wardcode' => 'required|is_unique[adx_ward.wardcode]',
            'llg_id' => 'required|numeric',
            'district_id' => 'required|numeric',
            'province_id' => 'required|numeric',
            'country_id' => 'required|numeric'
        ];

        if ($this->validate($rules)) {
            $data = [
                'name' => $this->request->getPost('name'),
                'wardcode' => $this->request->getPost('wardcode'),
                'llg_id' => $this->request->getPost('llg_id'),
                'district_id' => $this->request->getPost('district_id'),
                'province_id' => $this->request->getPost('province_id'),
                'country_id' => $this->request->getPost('country_id')
            ];

            if ($this->wardModel->insert($data)) {
                session()->setFlashdata('success', 'Ward "' . $data['name'] . '" has been added successfully');
            } else {
                session()->setFlashdata('error', 'Failed to add ward. Please try again.');
            }
        } else {
            session()->setFlashdata('error', 'Validation failed: ' . implode(', ', $this->validator->getErrors()));
        }
        return redirect()->back();
    }

    public function wardUpdate()
    {
        $id = $this->request->getPost('id');

        $rules = [
            'name' => 'required|min_length[3]|max_length[100]',
            'wardcode' => 'required|is_unique[adx_ward.wardcode,id,' . $id . ']'
        ];

        if ($this->validate($rules)) {
            $data = [
                'name' => $this->request->getPost('name'),
                'wardcode' => $this->request->getPost('wardcode')
            ];

            if ($this->wardModel->update($id, $data)) {
                session()->setFlashdata('success', 'Ward updated successfully');
            } else {
                session()->setFlashdata('error', 'Failed to update ward');
            }
        } else {
            session()->setFlashdata('error', 'Validation failed: ' . implode(', ', $this->validator->getErrors()));
        }
        return redirect()->back();
    }

    public function wardDelete($id)
    {
        $ward = $this->wardModel->find($id);
        if ($ward) {
            if ($this->wardModel->delete($id)) {
                session()->setFlashdata('success', 'Ward "' . $ward['name'] . '" has been deleted successfully');
            } else {
                session()->setFlashdata('error', 'Failed to delete ward. Please try again.');
            }
        } else {
            session()->setFlashdata('error', 'Ward not found');
        }
        return redirect()->back();
    }
    */

    // Education
    public function educationCreate()
    {
        $data = [
            'name' => $this->request->getPost('name'),
            'icon' => $this->request->getPost('icon'),
            'color_code' => $this->request->getPost('color_code'),
            'remarks' => $this->request->getPost('remarks'),
            'created_by' => session()->get('user_id')
        ];

        if ($this->educationModel->insert($data)) {
            session()->setFlashdata('success', 'Education item added successfully');
        } else {
            session()->setFlashdata('error', 'Failed to add education item');
        }
        return redirect()->to('dakoii/dashboard');
    }

    public function educationUpdate()
    {
        $id = $this->request->getPost('id');
        $data = [
            'name' => $this->request->getPost('name'),
            'icon' => $this->request->getPost('icon'),
            'color_code' => $this->request->getPost('color_code'),
            'remarks' => $this->request->getPost('remarks'),
            'updated_by' => session()->get('user_id')
        ];

        if ($this->educationModel->update($id, $data)) {
            session()->setFlashdata('success', 'Education item updated successfully');
        } else {
            session()->setFlashdata('error', 'Failed to update education item');
        }
        return redirect()->to('dakoii/dashboard');
    }

    // Exercise Management Methods - View Functions Only

    /**
     * [GET] Display exercise creation form
     */
    public function exerciseCreateForm($orgcode)
    {
        $organization = $this->dakoiiOrgModel->where('org_code', $orgcode)->first();
        if (!$organization) {
            session()->setFlashdata('error', 'Organization not found');
            return redirect()->to('dakoii/organization/list');
        }

        // Map database fields to view-expected format
        $org = [
            'id' => $organization['id'],
            'name' => $organization['org_name'],
            'org_name' => $organization['org_name'],
            'org_code' => $organization['org_code'],
            'orgcode' => $organization['org_code']
        ];

        $data['title'] = "Create Exercise";
        $data['menu'] = "organizations";
        $data['org'] = $org;

        echo view('dakoii/dakoii_exercise_create', $data);
    }

    /**
     * [GET] Display exercise details
     */
    public function exerciseView($exerciseId)
    {
        $exercise = $this->exerciseModel->find($exerciseId);
        if (!$exercise) {
            session()->setFlashdata('error', 'Exercise not found');
            return redirect()->to('dakoii/dashboard');
        }

        // Get organization information
        $organization = $this->dakoiiOrgModel->find($exercise['org_id']);
        if ($organization) {
            $exercise['org_name'] = $organization['org_name'];
            $exercise['orgcode'] = $organization['org_code'];
        }

        $data['title'] = "Exercise Details";
        $data['menu'] = "organizations";
        $data['exercise'] = $exercise;

        echo view('dakoii/dakoii_exercise_view', $data);
    }

    /**
     * [GET] Display exercise edit form
     */
    public function exerciseEditForm($exerciseId)
    {
        $exercise = $this->exerciseModel->find($exerciseId);
        if (!$exercise) {
            session()->setFlashdata('error', 'Exercise not found');
            return redirect()->to('dakoii/dashboard');
        }

        // Get organization information
        $organization = $this->dakoiiOrgModel->find($exercise['org_id']);
        if (!$organization) {
            session()->setFlashdata('error', 'Organization not found');
            return redirect()->to('dakoii/dashboard');
        }

        // Map database fields to view-expected format
        $org = [
            'id' => $organization['id'],
            'name' => $organization['org_name'],
            'org_name' => $organization['org_name'],
            'org_code' => $organization['org_code'],
            'orgcode' => $organization['org_code']
        ];

        $data['title'] = "Edit Exercise";
        $data['menu'] = "organizations";
        $data['exercise'] = $exercise;
        $data['org'] = $org;

        echo view('dakoii/dakoii_exercise_edit', $data);
    }

    /**
     * [POST] Change the status of an exercise
     */
    public function exerciseChangeStatus($exerciseId)
    {
        $status = $this->request->getVar('status');

        // Validate the status
        $validStatuses = ['publish', 'draft', 'selection', 'review', 'publish_request', 'closed'];
        if (!in_array($status, $validStatuses)) {
            session()->setFlashdata('error', 'Invalid status value');
            return redirect()->to(base_url('dakoii/dashboard'));
        }

        // Get the exercise
        $exercise = $this->exerciseModel->find($exerciseId);
        if (!$exercise) {
            session()->setFlashdata('error', 'Exercise not found');
            return redirect()->to(base_url('dakoii/dashboard'));
        }

        // Update the exercise status
        try {
            $this->exerciseModel->update($exerciseId, [
                'status' => $status,
                'updated_by' => session()->get('user_id')
            ]);

            session()->setFlashdata('success', 'Exercise status updated successfully to ' . ucfirst(str_replace('_', ' ', $status)));
        } catch (\Exception $e) {
            session()->setFlashdata('error', 'Failed to update exercise status: ' . $e->getMessage());
            log_message('error', 'Exercise status update exception: ' . $e->getMessage());
        }

        // Check if there's a redirect parameter
        $redirect = $this->request->getGet('redirect');
        if ($redirect && strpos($redirect, 'org/') === 0) {
            $orgcode = substr($redirect, 4);
            return redirect()->to(base_url('dakoii/organization/view/' . $orgcode));
        }

        return redirect()->to(base_url('dakoii/dashboard'));
    }

}
