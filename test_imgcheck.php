<?php

// Simple test script to verify imgcheck function
require_once 'app/Config/Paths.php';
require_once 'app/Helpers/info_helper.php';

// Test cases
$test_cases = [
    'public/uploads/org_logo/2345_1742946858.jpg',
    'uploads/org_logo/2345_1742946858.jpg',
    '/uploads/org_logo/2345_1742946858.jpg',
    'public/assets/system_img/no-img.jpg',
    'assets/system_img/no-img.jpg',
    'nonexistent/file.jpg',
    '',
    null
];

echo "Testing imgcheck() function:\n\n";

foreach ($test_cases as $test_path) {
    echo "Input: " . var_export($test_path, true) . "\n";
    echo "Output: " . imgcheck($test_path) . "\n";
    echo "---\n";
}
